(function() {
"use strict";
// ==================== RPG Editor 生成的修改代码 ====================
// 此代码由 RPG Editor 自动生成，请勿手动修改
// 生成时间: 2025/5/29 17:56:06



// ==================== 对象查找方法 ====================
    // 获取插件参数
    const parameters = PluginManager.parameters("RPGEditor_PrototypeModifications");
    const DEBUG = parameters["debug"] === "true";

    // 调试日志函数
    function log(message, ...args) {
        if (DEBUG) {
            console.log("[RPGEditor]", message, ...args);
        }
    }
    // 工具函数：根据场景路径查找对象
    function findObjectByScenePath(scenePath) {
        if (!scenePath || scenePath.length === 0) return null;

        let current = SceneManager._scene;
        if (!current) return null;

        // 检查第一个元素是否为创建操作标记
        const firstElement = scenePath[0];
        let isCreationOperation = firstElement === "+";

        // 确定实际的场景路径起始位置
        let startIndex = isCreationOperation ? 1 : 0;

        // 验证场景名称
        const expectedSceneName = scenePath[startIndex];
        const actualSceneName = current.constructor.name;
        if (actualSceneName !== expectedSceneName) {
            if (DEBUG) console.log(`[Scene Mismatch] Expected: ${expectedSceneName}, Actual: ${actualSceneName}`);
            return null;
        }

        // 确定遍历的结束位置
        let endIndex = scenePath.length;
        if (isCreationOperation) {
            // 创建操作：最后一个索引不查找，那是要创建的位置
            endIndex = scenePath.length - 1;
            if (DEBUG) console.log(`[Creation Operation] Finding parent object, skipping last index: ${scenePath[scenePath.length - 1]}`);
        }

        // 遍历路径
        for (let i = startIndex + 1; i < endIndex; i++) {
            const index = parseInt(scenePath[i]);
            if (isNaN(index) || !current.children || !current.children[index]) {
                if (DEBUG) console.log(`[Path Break] Index ${index} does not exist in ${current.constructor.name}`);
                return null;
            }
            current = current.children[index];
        }

        return current;
    }


    // 工具函数：创建滤镜实例
    function createFilterInstance(filterType, params = {}) {
        if (DEBUG) log(`[Filter] Creating filter: ${filterType}`, params);

        // 获取全局 PIXI 对象
        const PIXI = window.PIXI;
        if (!PIXI) {
            console.error('[Filter] PIXI 对象不可用');
            return null;
        }

        let filter = null;

        try {
            switch (filterType) {
                case 'blur':
                    if (PIXI.filters && PIXI.filters.BlurFilter) {
                        filter = new PIXI.filters.BlurFilter(params.blur || 2, params.quality || 4);
                    } else if (PIXI.BlurFilter) {
                        filter = new PIXI.BlurFilter(params.blur || 2, params.quality || 4);
                    }
                    break;

                case 'alpha':
                    if (PIXI.filters && PIXI.filters.AlphaFilter) {
                        filter = new PIXI.filters.AlphaFilter(params.alpha || 1);
                    } else if (PIXI.AlphaFilter) {
                        filter = new PIXI.AlphaFilter(params.alpha || 1);
                    }
                    break;

                case 'colorMatrix':
                    if (PIXI.filters && PIXI.filters.ColorMatrixFilter) {
                        filter = new PIXI.filters.ColorMatrixFilter();
                    } else if (PIXI.ColorMatrixFilter) {
                        filter = new PIXI.ColorMatrixFilter();
                    }
                    if (filter && params.matrix) {
                        filter.matrix = params.matrix;
                    }
                    break;

                case 'noise':
                    if (PIXI.filters && PIXI.filters.NoiseFilter) {
                        filter = new PIXI.filters.NoiseFilter(params.noise || 0.5, params.seed || Math.random());
                    } else if (PIXI.NoiseFilter) {
                        filter = new PIXI.NoiseFilter(params.noise || 0.5, params.seed || Math.random());
                    }
                    break;

                case 'advancedColor':
                    if (window.AdvancedColorFilter) {
                        filter = new window.AdvancedColorFilter();
                        // 设置高级色彩滤镜参数
                        if (params.contrast !== undefined) filter.contrast = params.contrast;
                        if (params.brightness !== undefined) filter.brightness = params.brightness;
                        if (params.shadows !== undefined) filter.shadows = params.shadows;
                        if (params.highlights !== undefined) filter.highlights = params.highlights;
                        if (params.saturation !== undefined) filter.saturation = params.saturation;
                        if (params.vibrance !== undefined) filter.vibrance = params.vibrance;
                    }
                    break;

                case 'smoke':
                    if (window.SmokeFilter) {
                        filter = new window.SmokeFilter();
                        if (params.intensity !== undefined) filter.intensity = params.intensity;
                    }
                    break;

                case 'fire':
                    if (window.FireFilter) {
                        filter = new window.FireFilter();
                        if (params.intensity !== undefined) filter.intensity = params.intensity;
                        if (params.fireColor !== undefined) filter.fireColor = params.fireColor;
                    }
                    break;

                case 'water':
                    if (window.WaterFilter) {
                        filter = new window.WaterFilter();
                        if (params.intensity !== undefined) filter.intensity = params.intensity;
                        if (params.amplitude !== undefined) filter.amplitude = params.amplitude;
                        if (params.time !== undefined) filter.time = params.time;
                    }
                    break;

                case 'glow':
                    if (window.GlowFilter) {
                        filter = new window.GlowFilter();
                        if (params.intensity !== undefined) filter.intensity = params.intensity;
                        if (params.glowColor !== undefined) filter.glowColor = params.glowColor;
                    }
                    break;

                case 'cloth':
                    if (window.ClothFilter) {
                        filter = new window.ClothFilter();
                        if (params.intensity !== undefined) filter.intensity = params.intensity;
                        if (params.waveFrequency !== undefined) filter.waveFrequency = params.waveFrequency;
                        if (params.time !== undefined) filter.time = params.time;
                    }
                    break;

                default:
                    console.warn(`[Filter] 不支持的滤镜类型: ${filterType}`);
                    return null;
            }

            if (filter) {
                // 设置通用属性
                if (params.enabled !== undefined) {
                    filter.enabled = params.enabled;
                }

                if (DEBUG) log(`[Filter] 成功创建滤镜: ${filterType}`, filter);
            } else {
                console.error(`[Filter] 创建滤镜失败: ${filterType}`);
            }

        } catch (error) {
            console.error(`[Filter] 创建滤镜时出错: ${filterType}`, error);
            return null;
        }

        return filter;
    }

    // 工具函数：创建滤镜数组
    function createFiltersArray(filtersData) {
        if (!Array.isArray(filtersData)) {
            return [];
        }

        const filters = [];
        for (const filterData of filtersData) {
            if (filterData && filterData.type && filterData.params) {
                const filter = createFilterInstance(filterData.type, filterData.params);
                if (filter) {
                    filters.push(filter);
                }
            }
        }

        if (DEBUG) log(`[Filter] 创建了 ${filters.length} 个滤镜`);
        return filters;
    }

    // 工具函数：创建游戏对象
    function createGameObject(type, params = {}) {
        if (DEBUG) log(`[GameObject] Creating new object: ${type}`, params);

        switch (type) {
            case "Sprite":
                const sprite = new Sprite();
                sprite.name = params.name || "NewSprite";
                sprite.x = params.x || 0;
                sprite.y = params.y || 0;
                sprite.visible = params.visible !== undefined ? params.visible : true;
                return sprite;

            case "Label":
                const label = new Sprite();
                label.name = params.name || "NewLabel";
                label.x = params.x || 0;
                label.y = params.y || 0;
                label.visible = params.visible !== undefined ? params.visible : true;

                const bitmap = new Bitmap(200, 40);
                bitmap.fontSize = 20;
                bitmap.textColor = "#ffffff";
                bitmap.outlineColor = "rgba(0, 0, 0, 0.5)";
                bitmap.outlineWidth = 4;
                bitmap.drawText(params.text || "New Text", 0, 0, 200, 40, "left");
                bitmap.text = params.text || "New Text";

                label.bitmap = bitmap;
                return label;

            case "Container":
                const container = new PIXI.Container();
                container.name = params.name || "NewContainer";
                container.x = params.x || 0;
                container.y = params.y || 0;
                container.visible = params.visible !== undefined ? params.visible : true;
                return container;

            case "Window":
                const rect = new Rectangle(0, 0, 200, 100);
                const windowObj = new Window_Base(rect);
                windowObj.name = params.name || "NewWindow";
                windowObj.x = params.x || 0;
                windowObj.y = params.y || 0;
                windowObj.visible = params.visible !== undefined ? params.visible : true;
                return windowObj;

            case "Button":
                const button = new Sprite_Clickable();
                button.name = params.name || "NewButton";
                button.x = params.x || 0;
                button.y = params.y || 0;
                button.visible = params.visible !== undefined ? params.visible : true;

                const buttonBitmap = new Bitmap(120, 40);
                buttonBitmap.fillRect(0, 0, 120, 40, "#3498db");
                buttonBitmap.fontSize = 18;
                buttonBitmap.textColor = "#ffffff";
                buttonBitmap.drawText(params.text || "Button", 0, 0, 120, 40, "center");

                button.bitmap = buttonBitmap;
                button._isButton = true;
                return button;

            case "LayoutContainer":
                if (typeof LayoutContainer !== "undefined") {
                    const layoutContainer = new LayoutContainer();
                    layoutContainer.name = params.name || "NewLayoutContainer";
                    layoutContainer.x = params.x || 0;
                    layoutContainer.y = params.y || 0;
                    layoutContainer.visible = params.visible !== undefined ? params.visible : true;
                    return layoutContainer;
                } else {
                    // 如果没有LayoutContainer类，创建一个普通的Container
                    const fallbackContainer = new PIXI.Container();
                    fallbackContainer.name = params.name || "NewLayoutContainer";
                    fallbackContainer.x = params.x || 0;
                    fallbackContainer.y = params.y || 0;
                    fallbackContainer.visible = params.visible !== undefined ? params.visible : true;
                    return fallbackContainer;
                }

            default:
                if (DEBUG) log(`[GameObject] Unknown object type: ${type}`);
                return null;
        }
    }
// ==================== Scene_Title 场景修改 ====================

// 修改 Scene_Title 场景
const originalScene_TitleStart = Scene_Title.prototype.start;
Scene_Title.prototype.start = function() {
    // 调用原始方法
    originalScene_TitleStart.apply(this, arguments);

    if (DEBUG) console.log('Scene_Title.start 被调用');

    // 设置 Sprite 的 1 个属性
    const target_sprite_Scene_Title_2Path = ["Scene_Title","2"];
    const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);
    if (target_sprite_Scene_Title_2) {
        // 设置滤镜数组 (1 个滤镜)
        const filtersData = [{"type":"blur","params":{"blur":8,"quality":4,"enabled":true},"enabled":true}];
        target_sprite_Scene_Title_2.filters = createFiltersArray(filtersData);
        if (DEBUG) console.log('设置滤镜数组，滤镜数量:', target_sprite_Scene_Title_2.filters.length);
        // 滤镜类型: blur
    }

};

// ==================== 代码生成完成 ====================
})();